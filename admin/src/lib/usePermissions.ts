'use client';

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { axiosInstance } from '@/lib/axios';

interface User {
  id: string;
  name: string | null;
  email: string;
  role: {
    id: string;
    name: string;
    permissions: string[];
  } | null;
}

export function usePermissions() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchUserPermissions = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.get('/auth-admin/me');
      const data = response.data;
      
      if (!data.success || !data.data) {
        throw new Error('Invalid response format from /auth-admin/me');
      }
      setUser(data.data);
    } catch (error: any) {
      console.error('Fetch permissions error:', error);
      if (error.response?.status !== 401) {
        toast.error(error.message || 'Failed to fetch permissions');
      }
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUserPermissions();
  }, [fetchUserPermissions]);

  const hasPermission = useCallback((permission: string): boolean => {
    return !!user?.role?.permissions?.includes(permission) || user?.email === '<EMAIL>';
  }, [user]);

  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    return permissions.some((perm) => user?.role?.permissions?.includes(perm)) || user?.email === '<EMAIL>';
  }, [user]);

  return { user, isLoading, hasPermission, hasAnyPermission, refreshPermissions: fetchUserPermissions };
}