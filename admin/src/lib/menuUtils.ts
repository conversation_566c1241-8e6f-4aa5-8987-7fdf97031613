import { LucideIcon } from 'lucide-react';

interface MenuItem {
  title: string;
  url: string;
  icon: LucideIcon;
  permission?: string; // Required permission for this item
}

interface MenuGroup {
  title: string;
  icon: LucideIcon;
  children: MenuItem[];
  permissions?: string[]; // At least one permission required for the group
}

export function filterMenuByPermissions(
  menu: MenuGroup[],
  hasPermission: (permission: string) => boolean,
  hasAnyPermission: (permissions: string[]) => boolean
): MenuGroup[] {
  console.log('Filtering menu by permissions');
  return menu
    .map((group) => {
      // Skip group if it has permissions and user lacks all of them
      if (group.permissions && !hasAnyPermission(group.permissions)) {
        console.log(`Skipping group "${group.title}" due to missing permissions: [${group.permissions.join(', ')}]`);
        return null;
      }

      // Filter children based on individual permissions
      const filteredChildren = group.children.filter((item) => {
        if (!item.permission) {
          console.log(`Including item "${item.title}" (no permission required)`);
          return true;
        }
        const hasPerm = hasPermission(item.permission);
        console.log(`Checking item "${item.title}" permission "${item.permission}": ${hasPerm}`);
        return hasPerm;
      });

      // Skip group if it has no children and requires permissions
      if (filteredChildren.length === 0 && group.permissions) {
        console.log(`Skipping group "${group.title}" due to no valid children`);
        return null;
      }

      return {
        ...group,
        children: filteredChildren,
      };
    })
    .filter((group): group is MenuGroup => {
      const isIncluded = group !== null;
      if (isIncluded) {
        console.log(`Including group "${group.title}" with ${group.children.length} children`);
      }
      return isIncluded;
    });
}