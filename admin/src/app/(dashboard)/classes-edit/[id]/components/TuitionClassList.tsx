'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Trash2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { usePermissions } from '@/lib/usePermissions';

interface TuitionClass {
  id: string;
  education: string;
  coachingType: string;
  boardType: string;
  medium: string;
  section: string;
  subject: string;
  details: string;
}

interface TuitionClassListProps {
  tuitionClasses: TuitionClass[];
  handleDeleteTuitionClass: (tuitionId: string) => void;
  parseFieldValue: (value: string | null | undefined) => string;
}

const TuitionClassList: React.FC<TuitionClassListProps> = ({
  tuitionClasses,
  handleDeleteTuitionClass,
  parseFieldValue,
}) => {
  const { hasPermission } = usePermissions();
  const canDeleteClasses = hasPermission('delete classes');

  if (tuitionClasses.length === 0) {
    return <p className="text-gray-500">No tuition classes found.</p>;
  }

  return (
    <>
      {tuitionClasses.map((tuition, index) => (
        <div key={tuition.id || index} className="border border-gray-200 rounded-lg p-4 space-y-4">
          <div className="flex justify-between items-start">
            <h3 className="text-md font-medium text-gray-700">Tuition Class {index + 1}</h3>
            {canDeleteClasses && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                  </Button>
                </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Tuition Class</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete this tuition class? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDeleteTuitionClass(tuition.id)}
                    className="bg-red-500 hover:bg-red-600"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
            )}
          </div>
            
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-black font-medium">Education</Label>
              <div className="p-2 bg-gray-50 rounded border text-black">
                {tuition.education || 'Not specified'}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-black font-medium">Coaching Type</Label>
              <div className="p-2 bg-gray-50 rounded border text-black">
                {parseFieldValue(tuition.coachingType)}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-black font-medium">Board Type</Label>
              <div className="p-2 bg-gray-50 rounded border text-black">
                {parseFieldValue(tuition.boardType)}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-black font-medium">Subject</Label>
              <div className="p-2 bg-gray-50 rounded border text-black">
                {parseFieldValue(tuition.subject)}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-black font-medium">Medium</Label>
              <div className="p-2 bg-gray-50 rounded border text-black">
                {parseFieldValue(tuition.medium)}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-black font-medium">Section</Label>
              <div className="p-2 bg-gray-50 rounded border text-black">
                {parseFieldValue(tuition.section)}
              </div>
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label className="text-black font-medium">Details</Label>
              <div className="p-2 bg-gray-50 rounded border text-black">
                {parseFieldValue(tuition.details)}
              </div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default TuitionClassList;
