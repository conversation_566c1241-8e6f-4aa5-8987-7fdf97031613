"use client";

import React from 'react';
import AdminChat from '@/app-components/AdminChat';
import { usePermissions } from '@/lib/usePermissions';

const ChatPage = () => {
  const { hasPermission, isLoading: permissionsLoading } = usePermissions();
  const canManageChats = hasPermission('manage chats');

  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!canManageChats) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You do not have permission to manage chats.</p>
        </div>
      </div>
    );
  }

  return (
    <AdminChat
        isAuthenticated={true}
        loginPath="/login"
    />
  );
};

export default ChatPage;