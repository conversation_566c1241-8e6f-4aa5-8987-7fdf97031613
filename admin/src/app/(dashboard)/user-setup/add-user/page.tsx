'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Users, Pencil, Trash2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { usePermissions } from '@/lib/usePermissions';
import { axiosInstance } from '@/lib/axios';

interface User {
  id: string;
  name: string;
  email: string;
  role: { id: string; name: string } | null;
  createdAt: string;
}

interface Role {
  id: string;
  name: string;
}

const PAGE_SIZE = 10;

const AddUser = () => {
  const { hasPermission, isLoading: permissionsLoading } = usePermissions();
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [editUserId, setEditUserId] = useState<string | null>(null);

  // Permission checks
  const canReadUsers = hasPermission('read users');
  const canCreateUsers = hasPermission('create users');
  const canUpdateUsers = hasPermission('update users');
  const canDeleteUsers = hasPermission('delete users');
  const [addFormData, setAddFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    roleId: '',
  });
  const [editFormData, setEditFormData] = useState({
    name: '',
    email: '',
    roleId: '',
  });

  const fetchUsers = useCallback(async (page: number) => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.get('/users', {
        params: { page, pageSize: PAGE_SIZE },
        withCredentials: true,
      });
      const data = response.data;
      const usersData = data.data?.users || [];
      setUsers(usersData);
      setCurrentPage(page);
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to fetch users');
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchRoles = useCallback(async () => {
    try {
      const response = await axiosInstance.get('/roles', {
        withCredentials: true,
      });
      const data = response.data;
      setRoles(data.data?.roles || []);
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to fetch roles');
    }
  }, []);

  const fetchUserForEdit = useCallback(async (id: string) => {
    try {
      const response = await axiosInstance.get(`/users/${id}`, {
        withCredentials: true,
      });
      const data = response.data;
      setEditFormData({
        name: data.data.name || '',
        email: data.data.email || '',
        roleId: data.data.roleId || '',
      });
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to fetch user');
    }
  }, []);

  useEffect(() => {
    fetchUsers(currentPage);
    fetchRoles();
  }, [fetchUsers, fetchRoles, currentPage]);

  // Early returns moved after all hooks
  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span>Loading permissions...</span>
      </div>
    );
  }

  if (!canReadUsers) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don&apos;t have permission to view users.</p>
        </div>
      </div>
    );
  }

  const handleDeleteClick = (id: string) => {
    setDeletingUserId(id);
    setIsConfirmDialogOpen(true);
  };

  const handleEditClick = (id: string) => {
    setEditUserId(id);
    fetchUserForEdit(id);
    setIsEditOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingUserId) return;
    try {
      await axiosInstance.delete(`/users/${deletingUserId}`, {
        withCredentials: true,
      });
      setUsers(users.filter((user) => user.id !== deletingUserId));
      toast.success('User deleted successfully');
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to delete user');
    } finally {
      setDeletingUserId(null);
      setIsConfirmDialogOpen(false);
    }
  };

  const handleAddInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAddFormData({ ...addFormData, [e.target.name]: e.target.value });
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditFormData({ ...editFormData, [e.target.name]: e.target.value });
  };

  const handleAddRoleChange = (value: string) => {
    setAddFormData({ ...addFormData, roleId: value });
  };

  const handleEditRoleChange = (value: string) => {
    setEditFormData({ ...editFormData, roleId: value });
  };

  const handleAddSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (addFormData.password !== addFormData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    if (!addFormData.roleId) {
      toast.error('Please select a role');
      return;
    }
    try {
      const payload = {
        name: `${addFormData.firstName} ${addFormData.lastName}`.trim(),
        email: addFormData.email,
        password: addFormData.password,
        roleId: addFormData.roleId,
      };
      await axiosInstance.post('/users', payload, { withCredentials: true });
      toast.success('User created successfully');
      setIsAddOpen(false);
      setAddFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: '',
        roleId: '',
      });
      fetchUsers(currentPage);
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to create user');
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editUserId) return;
    try {
      const payload = {
        name: editFormData.name,
        email: editFormData.email,
        roleId: editFormData.roleId,
      };
      await axiosInstance.put(`/users/${editUserId}`, payload, { withCredentials: true });
      toast.success('User updated successfully');
      setIsEditOpen(false);
      setEditUserId(null);
      setEditFormData({ name: '', email: '', roleId: '' });
      fetchUsers(currentPage);
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to update user');
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => <span className="font-medium">{row.original.name || 'N/A'}</span>,
    },
    {
      accessorKey: 'email',
      header: 'Email',
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => row.original.role?.name || 'N/A',
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => formatDate(row.original.createdAt),
    },
    {
      id: 'actions',
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="flex justify-end gap-2">
          {canUpdateUsers && (
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleEditClick(row.original.id)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
          )}
          {canDeleteUsers && (
            <Button
              variant="destructive"
              size="icon"
              onClick={() => handleDeleteClick(row.original.id)}
              disabled={row.original.role?.name === 'Super Admin'}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  const handleEditDialogOpenChange = (open: boolean) => {
    setIsEditOpen(open);
    if (!open) {
      setEditUserId(null);
      setEditFormData({ name: '', email: '', roleId: '' });
    }
  };

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            User Management
          </h1>
          <div className="flex gap-4">
            {canCreateUsers && (
              <Dialog open={isAddOpen} onOpenChange={setIsAddOpen}>
                <DialogTrigger asChild>
                  <Button className="text-base h-10">
                    <Users className="h-5 w-5 mr-2" />
                    Add User
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-[90vw] sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="text-2xl">Add New User</DialogTitle>
                    <div className="border-b border-gray-200 w-full mb-4"></div>
                  </DialogHeader>
                  <form className="space-y-4" onSubmit={handleAddSubmit}>
                    <div className="grid gap-2">
                      <Label htmlFor="firstName" className="text-base">First Name</Label>
                      <Input
                        id="firstName"
                        name="firstName"
                        placeholder="Enter first name"
                        value={addFormData.firstName}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="lastName" className="text-base">Last Name</Label>
                      <Input
                        id="lastName"
                        name="lastName"
                        placeholder="Enter last name"
                        value={addFormData.lastName}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="email" className="text-base">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Enter email"
                        value={addFormData.email}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="password" className="text-base">Password</Label>
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        placeholder="Enter password"
                        value={addFormData.password}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="confirmPassword" className="text-base">Confirm Password</Label>
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        placeholder="Confirm password"
                        value={addFormData.confirmPassword}
                        onChange={handleAddInputChange}
                        required
                        className="text-base p-2 h-10"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="role" className="text-base">Role</Label>
                      <Select name="role" onValueChange={handleAddRoleChange} value={addFormData.roleId}>
                        <SelectTrigger id="role" className="text-base p-2 h-10">
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.length === 0 ? (
                            <div className="text-sm text-gray-500 p-2">No roles available. Create a role first.</div>
                          ) : (
                            roles.map((role) => (
                              <SelectItem key={role.id} value={role.id} className="text-base">
                                {role.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex justify-end gap-4 pt-4 sticky bottom-0 bg-white">
                      <Button variant="outline" onClick={() => setIsAddOpen(false)} className="text-base h-10">
                        Cancel
                      </Button>
                      <Button type="submit" className="text-base h-10">
                        Add User
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>
        <DataTable
          columns={columns}
          data={users}
          isLoading={isLoading}
        />
        <Dialog open={isEditOpen} onOpenChange={handleEditDialogOpenChange}>
          <DialogContent className="max-w-[90vw] sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl">Edit User</DialogTitle>
              <div className="border-b border-gray-200 w-full mb-4"></div>
            </DialogHeader>
            <form className="space-y-4" onSubmit={handleEditSubmit}>
              <div className="grid gap-2">
                <Label htmlFor="name" className="text-base">Name</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter name"
                  value={editFormData.name}
                  onChange={handleEditInputChange}
                  required
                  className="text-base p-2 h-10"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email" className="text-base">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter email"
                  value={editFormData.email}
                  onChange={handleEditInputChange}
                  required
                  className="text-base p-2 h-10"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="role" className="text-base">Role</Label>
                <Select name="role" onValueChange={handleEditRoleChange} value={editFormData.roleId}>
                  <SelectTrigger id="role" className="text-base p-2 h-10">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.length === 0 ? (
                      <div className="text-sm text-gray-500 p-2">No roles available.</div>
                    ) : (
                      roles.map((role) => (
                        <SelectItem key={role.id} value={role.id} className="text-base">
                          {role.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-4 pt-4 sticky bottom-0 bg-white">
                <Button variant="outline" onClick={() => handleEditDialogOpenChange(false)} className="text-base h-10">
                  Cancel
                </Button>
                <Button type="submit" className="text-base h-10">
                  Update User
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
        <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the user.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteConfirm}>Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default AddUser;