'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Users, Trash2, Pencil } from 'lucide-react';
import { axiosInstance } from '@/lib/axios';
import { usePermissions } from '@/lib/usePermissions';

interface Role {
  id: string;
  name: string;
  permissions: { permissionName: string }[];
}

interface PermissionModule {
  [key: string]: string;
}

interface Permissions {
  [module: string]: PermissionModule;
}

const PAGE_SIZE = 10;

const RolePage = () => {
  const { hasPermission, isLoading: permissionsLoading } = usePermissions();
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permissions | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPermissionsLoading, setIsPermissionsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [isOpen, setIsOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [deletingRoleId, setDeletingRoleId] = useState<string | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [roleName, setRoleName] = useState('');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  // Permission checks
  const canReadRoles = hasPermission('read roles');
  const canCreateUsers = hasPermission('create role');
  const canUpdateUsers = hasPermission('update role');
  const canDeleteUsers = hasPermission('delete role');

  const fetchPermissions = useCallback(async () => {
    try {
      setIsPermissionsLoading(true);
      const response = await axiosInstance.get('/constants/db-permissions', {
        withCredentials: true,
      });
      const data = response.data;
      if (!data.data.permissions || Object.keys(data.data.permissions).length === 0) {
        throw new Error('No permissions returned from server');
      }
      setPermissions(data.data.permissions);
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to fetch permissions');
    } finally {
      setIsPermissionsLoading(false);
    }
  }, []);

  const fetchRoles = useCallback(async (page: number) => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.get('/roles', {
        params: { page, pageSize: PAGE_SIZE },
        withCredentials: true,
      });
      const data = response.data;
      setRoles(data.data.roles || []);
      setCurrentPage(page);
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to fetch roles');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchRoleById = async (id: string) => {
    try {
      const response = await axiosInstance.get(`/roles/${id}`, {
        withCredentials: true,
      });
      const data = response.data;
      setRoleName(data.data.name);
      setSelectedPermissions(data.data.permissions.map((p: { permissionName: string }) => p.permissionName));
      setEditingRoleId(id);
      setIsEditMode(true);
      setIsOpen(true);
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to fetch role');
    }
  };

  useEffect(() => {
    if (canReadRoles) {
      fetchPermissions();
      fetchRoles(currentPage);
    }
  }, [fetchPermissions, fetchRoles, currentPage, canReadRoles]);

  const handleDeleteClick = (id: string) => {
    setDeletingRoleId(id);
    setIsConfirmDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingRoleId) return;
    try {
      await axiosInstance.delete(`/roles/${deletingRoleId}`, {
        withCredentials: true,
      });
      setRoles(roles.filter((role) => role.id !== deletingRoleId));
      toast.success('Role deleted successfully');
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || 'Failed to delete role');
    } finally {
      setDeletingRoleId(null);
      setIsConfirmDialogOpen(false);
    }
  };

  const handleSaveRole = async () => {
    if (!roleName) {
      toast.error('Role name is required');
      return;
    }
    try {
      const payload = {
        name: roleName,
        permissions: selectedPermissions,
      };
      if (isEditMode && editingRoleId) {
        await axiosInstance.put(`/roles/${editingRoleId}`, payload, { withCredentials: true });
        toast.success('Role updated successfully');
      } else {
        await axiosInstance.post('/roles', payload, { withCredentials: true });
        toast.success('Role created successfully');
      }
      setIsOpen(false);
      setRoleName('');
      setSelectedPermissions([]);
      setIsEditMode(false);
      setEditingRoleId(null);
      fetchRoles(currentPage);
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error.message || `Failed to ${isEditMode ? 'update' : 'create'} role`);
    }
  };

  const handleSelectAll = (module: string) => {
    if (!permissions) return;
    const modulePermissions = Object.keys(permissions[module]);
    setSelectedPermissions((prev) =>
      prev.includes(modulePermissions[0])
        ? prev.filter((p) => !modulePermissions.includes(p))
        : [...new Set([...prev, ...modulePermissions])]
    );
  };

  const handleGlobalSelectAll = () => {
    if (!permissions) return;
    const allPermissions = Object.values(permissions).flatMap((module) => Object.keys(module));
    setSelectedPermissions((prev) =>
      prev.length === allPermissions.length ? [] : [...new Set(allPermissions)]
    );
  };

  const columns: ColumnDef<Role>[] = [
    {
      accessorKey: 'name',
      header: 'Role Name',
      cell: ({ row }) => <span className="font-medium">{row.original.name}</span>,
    },
    {
      id: 'actions',
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="flex justify-end gap-2">
          {canUpdateUsers && (
            <Button
              variant="outline"
              size="icon"
              onClick={() => fetchRoleById(row.original.id)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
          )}
          {canDeleteUsers && (
            <Button
              variant="destructive"
              size="icon"
              onClick={() => handleDeleteClick(row.original.id)}
              disabled={row.original.name === 'Super Admin'}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (permissionsLoading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!canReadRoles) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600">You do not have permission to view the roles management page.</p>
          </div>
        </div>
      </div>
    );
  }

  if (isPermissionsLoading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!permissions) {
    return (
      <div className="container mx-auto py-6 px-4">
        <h1 className="text-2xl font-bold flex items-center gap-2">
          <Users className="h-6 w-6" />
          Roles Management
        </h1>
        <div className="mt-4 flex items-center gap-4">
          <p className="text-red-600">Failed to load permissions. Please try again later.</p>
          <Button variant="outline" onClick={fetchPermissions}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            Roles Management
          </h1>
          <Dialog
            open={isOpen}
            onOpenChange={(open) => {
              setIsOpen(open);
              if (!open) {
                setIsEditMode(false);
                setEditingRoleId(null);
                setRoleName('');
                setSelectedPermissions([]);
              }
            }}
          >
            <DialogTrigger asChild>
              {canCreateUsers && (
                <Button className="text-base h-10">
                  <Users className="h-5 w-5 mr-2" />
                  Add Role
                </Button>
              )}
            </DialogTrigger>
            <DialogContent className="max-w-[90vw] sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="text-2xl">
                  {isEditMode ? 'Edit Role' : 'Add New Role'}
                </DialogTitle>
                <div className="border-b border-gray-200 w-full mb-4"></div>
              </DialogHeader>
              <div className="space-y-6">
                <div className="grid gap-2">
                  <Label htmlFor="name" className="text-base">
                    Role Name
                  </Label>
                  <Input
                    id="name"
                    placeholder="Enter role name"
                    value={roleName}
                    onChange={(e) => setRoleName(e.target.value)}
                    required
                    className="text-base p-2 h-10"
                  />
                </div>
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Module Permissions</h3>
                    <Button variant="outline" onClick={handleGlobalSelectAll}>
                      Select All Permissions
                    </Button>
                  </div>
                  <div className="space-y-4">
                    {Object.entries(permissions).map(([module, perms]) => (
                      <div key={module} className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="text-md font-medium">
                            {module.replace('_', ' ').toUpperCase()}
                          </h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSelectAll(module)}
                          >
                            Select All
                          </Button>
                        </div>
                        <div className="space-y-2">
                          {Object.entries(perms).map(([perm, label]) => (
                            <div key={perm} className="flex items-center">
                              <input
                                type="checkbox"
                                id={perm}
                                value={perm}
                                checked={selectedPermissions.includes(perm)}
                                onChange={(e) => {
                                  setSelectedPermissions((prev) =>
                                    e.target.checked
                                      ? [...new Set([...prev, perm])]
                                      : prev.filter((p) => p !== perm)
                                  );
                                }}
                                className="mr-2 h-4 w-4"
                              />
                              <label htmlFor={perm} className="text-sm">
                                {label}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-end gap-4 pt-4 sticky bottom-0 bg-white">
                  <Button
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                    className="text-base h-10"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveRole}
                    className="text-base h-10"
                  >
                    {isEditMode ? 'Update Role' : 'Save Role'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
        <DataTable
          columns={columns}
          data={roles}
          isLoading={isLoading}
        />
        <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the role.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteConfirm}>Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default RolePage;
