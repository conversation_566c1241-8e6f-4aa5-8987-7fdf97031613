"use client";

import React, { useEffect, useState, useCallback } from "react";
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  flexRender,
} from '@tanstack/react-table';
import Link from 'next/link';
import {
  DownloadIcon,
  Trash2Icon,
  EyeIcon,
  Edit,
  UsersRound,
} from 'lucide-react';
import dynamic from 'next/dynamic';
const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { axiosInstance } from "@/lib/axios";
import "react-quill-new/dist/quill.snow.css";
import { TbMailFilled } from "react-icons/tb";
import { toast } from "sonner";
import { sendMail } from "@/services/email";
import { downloadClassesExcel } from "@/services/classesApi";
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { usePermissions } from '@/lib/usePermissions';
import Pagination from "@/app-components/pagination";

import ConfirmDialog from "@/app-components/ConfirmDialog";

type ClassData = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  className: string | null;
  userName: string | null;
  status?: string;
  contactNo?: string; // Added to align with column definition
  coins?: number; // Added to align with column definition
};

export function ClassesTable() {
  const { hasPermission, isLoading: permissionsLoading } = usePermissions(); // Hook 1
  const [data, setData] = useState<ClassData[]>([]); // Hook 2
  const [globalFilter, setGlobalFilter] = useState(""); // Hook 3
  const [statusFilter, setStatusFilter] = useState("all"); // Hook 4
  const [page, setPage] = useState(1); // Hook 5
  const [isLoadingStats, setIsLoadingStats] = useState(true); // Hook 6
  const [totalPages, setTotalPages] = useState(1); // Hook 7
  const [subject, setSubject] = useState(''); // Hook 8
  const [value, setValue] = useState(''); // Hook 9
  const [loading, setLoading] = useState(false); // Hook 10
  const [status, setStatus] = useState(''); // Hook 11
  const [searchInput, setSearchInput] = useState(''); // Hook 12
  const [displayData, setDisplayData] = useState<{
    total: number;
    approved: number;
    pending: number;
    PROFILE_NOT_COMPLETED: number;
    rejected: number;
  }>({
    total: 0,
    approved: 0,
    pending: 0,
    PROFILE_NOT_COMPLETED: 0,
    rejected: 0,
  }); // Hook 13
  const [deleteClassId, setDeleteClassId] = useState<string | null>(null); // Hook 14

  const limit = 10;

  // Check permissions
  const canViewClasses = hasPermission('read classes');
  const canExportData = hasPermission('export data');
  const canDeleteClasses = hasPermission('delete classes');

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false); // Optional: for loader

  const fetchClasses = useCallback(async () => { // Hook 15
    if (!canViewClasses) {
      console.log('User does not have permission to view classes');
      return;
    }
    try {
      setIsLoadingStats(true);
      const response = await axiosInstance.get("/classes/getAll", {
        params: { page, limit, search: globalFilter, status: statusFilter },
      });
      console.log('fetchClasses response:', response.data.classes); // Debug log
      // Normalize userName if API returns username
      const normalizedData = response.data.classes.map((item: any) => ({
        ...item,
        userName: item.userName || item.username || 'N/A', // Fallback to username or 'N/A'
      }));
      setData(normalizedData || []);
      setTotalPages(response.data.totalPages || 1);
    } catch (error: any) {
      console.error("Failed to fetch classes:", error.message);
      toast.error(`Failed to load classes data: ${error.response?.data?.error || error.message}`);
    } finally {
      setIsLoadingStats(false);
    }
  }, [page, globalFilter, statusFilter, canViewClasses]);

  const fetchClassesAll = useCallback(async () => { // Hook 16
    if (!canViewClasses) {
      console.log('User does not have permission to view classes');
      return;
    }
    try {
      setIsLoadingStats(true);
      const res = await axiosInstance.get("/classes/getAllDisplayCount");
      console.log('fetchClassesAll response:', res.data); // Debug log
      setDisplayData({
        total: res.data.total || 0,
        approved: res.data.approved || 0,
        pending: res.data.pending || 0,
        PROFILE_NOT_COMPLETED: res.data.PROFILE_NOT_COMPLETED || res.data.profileNotCompleted || 0,
        rejected: res.data.rejected || 0,
      });
    } catch (error: any) {
      console.error('Error fetching display counts:', error.message);
      toast.error(`Failed to load stats data: ${error.response?.data?.error || error.message}`);
    } finally {
      setIsLoadingStats(false);
    }
  }, [canViewClasses]);

  const handleDelete = async () => {
    if (!deleteClassId) return;

    setIsDeleting(true);
    try {
      await axiosInstance.delete(`/classes/${deleteClassId}`);
      toast.success('Class deleted successfully');
      setDeleteClassId(null);
      setPage(1); // Reset to page 1 to ensure valid data after deletion
      await Promise.all([fetchClasses(), fetchClassesAll()]); // Refresh data
    } catch (error: any) {
      toast.error(`Failed to delete class: ${error.response?.data?.error || error.message}`);
      console.error('Delete error:', error.message);
    }
  };

  useEffect(() => { // Hook 17
    fetchClassesAll();
  }, [fetchClassesAll]);

  useEffect(() => { // Hook 18
    fetchClasses();
  }, [fetchClasses]);

  const handleFilter = () => {
    setPage(1);
    fetchClasses();
  };

  const handleDownloadExcel = async () => {
    try {
      await downloadClassesExcel({
        status: statusFilter,
        search: globalFilter,
      });
      toast.success('Excel file downloaded successfully');
    } catch (error: any) {
      toast.error(`Failed to download Excel file: ${error.response?.data?.error || error.message}`);
      console.error('Download error:', error.message);
    }
  };

  // Define columns
  const columns: ColumnDef<ClassData>[] = [
    {
      accessorKey: "userName",
      header: "User Name",
      cell: ({ row }) => row.original.userName || 'N/A', // Handle null/undefined
    },
    {
      accessorKey: "firstName",
      header: "First Name",
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
    },
    {
      accessorKey: "contactNo",
      header: "Contact No",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "className",
      header: "Class Name",
    },
    {
      accessorKey: "coins",
      header: "Uest Coins",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status || "PROFILE NOT COMPLETED";
        let colorClass = "bg-gray-200 text-gray-800";
        if (status === "approved") colorClass = "bg-green-200 text-green-800";
        else if (status === "rejected") colorClass = "bg-red-200 text-red-800";
        else if (status === "pending")
          colorClass = "bg-yellow-200 text-yellow-800";
        else if (status === "PROFILE NOT COMPLETED")
          colorClass = "bg-orange-200 text-orange-800";
        return (
          <span
            className={`capitalize rounded px-2 py-1 text-xs font-semibold ${colorClass}`}
          >
            {status.toLowerCase().replace("_", " ")}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2 items-center">
          <Link href={`/classes-student/${row.original.id}?name=${encodeURIComponent(row.original.className ?? '')}`}>
            <Button variant="outline" size="sm" className="p-1">
              <UsersRound className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/classes-details/${row.original.id}`}>
            <Button variant="outline" size="sm" className="p-1">
              <EyeIcon className="h-4 w-4" />
            </Button>
          </Link>
          {canDeleteClasses && (
            <>
              <Link href={`/classes-edit/${row.original.id}`}>
                <Button variant="ghost" size="sm" className="p-1">
                  <Edit className="h-4 w-4" />
                </Button>
              </Link>
              {row.original.status === "approved" ? (
                <Button variant="ghost" size="sm" className="p-1" disabled>
                  <Trash2Icon className="h-4 w-4 text-gray-400" />
                </Button>
              ) : (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="p-1 text-red-600 hover:text-red-700 text-red-500 hover:text-red-700 hover:bg-red-100"
                      onClick={() => {
                        setDeleteClassId(row.original.id);
                        setIsDeleteDialogOpen(true);
                      }}
                    >
                      <Trash2Icon className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <ConfirmDialog
                    open={isDeleteDialogOpen}
                    setOpen={setIsDeleteDialogOpen}
                    title="Are you sure?"
                    description="This will permanently delete the class."
                    confirmText="Delete"
                    cancelText="Cancel"
                    onConfirm={handleDelete}
                    isLoading={isDeleting}
                  />
                </Dialog>
              )}
            </>
          )}
        </div>
      ),
    },
  ];

  const table = useReactTable({ // Hook 19
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Early returns after all Hooks
  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!canViewClasses) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You do not have permission to view classes.</p>
        </div>
      </div>
    );
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSubject(name === "subject" ? value : subject);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await sendMail({
        subject: subject,
        message: value,
        status: status === "all" ? "" : status,
      });
      toast.success('Mail sent successfully!');
      setSubject('');
      setValue('');
    } catch (error: any) {
      toast.error(`Failed to send mail: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    {
      title: "TOTAL CLASS",
      value: isLoadingStats ? 'Loading...' : displayData.total,
    },
    {
      title: "APPROVED",
      value: isLoadingStats ? 'Loading...' : displayData.approved,
    },
    {
      title: "PENDING",
      value: isLoadingStats ? 'Loading...' : displayData.pending,
    },
    {
      title: "PROFILE NOT COMPLETED",
      value: isLoadingStats ? 'Loading...' : displayData.PROFILE_NOT_COMPLETED,
    },
    {
      title: "REJECTED",
      value: isLoadingStats ? 'Loading...' : displayData.rejected,
    },
  ];

  return (
    <div className="space-y-2 p-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-6 mb-6">
        {stats.map((stat, index) => (
          <Card key={index} className="bg-white rounded-xl shadow-md">
            <CardContent className="flex flex-col justify-center h-16 px-5">
              <div className="flex items-center justify-center">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                    {stat.title}
                  </CardTitle>
                </div>
              </div>
              <CardDescription className="text-xl font-semibold text-black text-center">
                {stat.value ?? '0'}
              </CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
      <hr />

      <div className="flex flex-wrap gap-4 mt-4 mb-5">
        <Input
          placeholder="Search by name, email, class..."
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          className="max-w-sm"
          onKeyDown={(e) => e.key === "Enter" && handleFilter()}
        />
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="PENDING">PENDING</SelectItem>
            <SelectItem value="APPROVED">APPROVED</SelectItem>
            <SelectItem value="REJECTED">REJECTED</SelectItem>
            <SelectItem value="NOT_COMPLETED">PROFILE NOT COMPLETED</SelectItem>
          </SelectContent>
        </Select>

        <div className="mt-1 flex items-center space-x-5">
          <Button
            onClick={() => {
              setGlobalFilter(searchInput);
              handleFilter();
            }}
          >
            Search
          </Button>

        <Dialog>
          <DialogTrigger asChild>
            <Button className="bg-orange-400 hover:bg-orange-600">
              <span className="flex items-center gap-2">
                <TbMailFilled className="ml-2" />
                Send Mail
              </span>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold">
                Send <span className="text-orange-400">Mail</span>
              </DialogTitle>
              <DialogDescription>Send To All Classes</DialogDescription>
            </DialogHeader>

              <form onSubmit={handleSubmit}>
                <Select onValueChange={(value) => setStatus(value)}>
                  <SelectTrigger className="w-full p-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 mb-2 focus:ring-orange-400">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="PENDING">PENDING</SelectItem>
                    <SelectItem value="APPROVED">APPROVED</SelectItem>
                    <SelectItem value="REJECTED">REJECTED</SelectItem>
                  </SelectContent>
                </Select>

                <div className="space-y-2">
                  <input
                    type="text"
                    placeholder="Enter Subject"
                    name="subject"
                    className="w-full p-3 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-400"
                    value={subject}
                    onChange={handleChange}
                  />
                  <ReactQuill
                    theme="snow"
                    value={value}
                    onChange={setValue}
                    style={{ height: "150px" }}
                    className="bg-white rounded-lg mb-16 w-[460px]"
                  />
                </div>

                <DialogFooter className="mt-4">
                  <Button type="submit" disabled={loading}>
                    {loading ? "Sending..." : "Send Mail"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>

        {canExportData && (
            <Button
            className="bg-orange-400 hover:bg-orange-600"
            onClick={handleDownloadExcel}
          >
              <span className="flex items-center gap-2">
                <DownloadIcon className="h-4 w-4 mr-1" /> Download xlsx
              </span>
            </Button>
        )}
        </div>
      </div>

      <div className="overflow-hidden rounded-lg border">
        <Table>
          <TableHeader className="sticky top-0 bg-muted">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center py-4">
                  No classes found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <Pagination
        page={page}
        totalPages={totalPages}
        setPage={setPage}
        entriesText={`${data.length} entries`}
      />
    </div>
  );
}