"use client";

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from '@/components/ui/button';
import { Trash2, Star, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { format } from 'date-fns';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Reviews } from '@/lib/types';
import { getReviews, deleteReview } from '@/services/reviewsApi';
import { usePermissions } from '@/lib/usePermissions';

const ReviewsPage = () => {
  const { hasPermission, isLoading: permissionsLoading } = usePermissions(); // Hook 1: usePermissions
  const canViewReviews = hasPermission('view reviews');
  const canDeleteReviews = hasPermission('delete reviews');
  const [reviews, setReviews] = useState<Reviews[]>([]); // Hook 2: useState
  const [isLoading, setIsLoading] = useState(true); // Hook 3: useState
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false); // Hook 4: useState
  const [reviewToDelete, setReviewToDelete] = useState<string | null>(null); // Hook 5: useState
  const [currentPage, setCurrentPage] = useState(1); // Hook 6: useState
  const [totalPages, setTotalPages] = useState(1); // Hook 7: useState
  const [totalItems, setTotalItems] = useState(0); // Hook 8: useState
  const [limit] = useState(10); // Not a Hook

  const fetchReviews = async (page: number, pageLimit: number) => {
    setIsLoading(true);
    try {
      const response = await getReviews(page, pageLimit);
      setReviews(response.testimonials);
      setTotalPages(response.totalPages);
      setTotalItems(response.total);
    } catch (error: any) {
      console.error('Error fetching Reviews:', error.message);
      toast.error('Failed to fetch reviews');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (permissionsLoading || !canViewReviews) return;
    fetchReviews(currentPage, limit);
  }, [currentPage, limit, permissionsLoading, canViewReviews]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const openDeleteDialog = (reviewId: string) => {
    setReviewToDelete(reviewId);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteTestimonial = async () => {
    if (!reviewToDelete) return;
    try {
      await deleteReview(reviewToDelete);
      toast.success('Review deleted successfully!');
      fetchReviews(currentPage, limit); // Refresh the data
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete review');
    } finally {
      setIsDeleteDialogOpen(false);
      setReviewToDelete(null);
    }
  };

  const extractContentFromMessage = (message: string) => {
    if (message.includes(':')) {
      return message.split(':').slice(1).join(':').trim();
    }
    return message;
  };

  // Early returns after all Hooks
  if (permissionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!canViewReviews) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You do not have permission to view reviews.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Reviews</h1>
      </div>
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>This action cannot be undone.</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDeleteTestimonial} className="bg-destructive text-destructive-foreground">
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : reviews.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-muted-foreground">No reviews found.</p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Review</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reviews.map((testimonial) => (
                <TableRow key={testimonial.id}>
                  <TableCell>{testimonial.studentName || 'N/A'}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${
                            i < testimonial.rating ? 'fill-yellow-500 text-yellow-500' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {extractContentFromMessage(testimonial.message)}
                  </TableCell>
                  <TableCell>{format(new Date(testimonial.createdAt), 'MMM dd, yyyy')}</TableCell>
                  <TableCell className="text-right">
                    {canDeleteReviews && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => openDeleteDialog(testimonial.id)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-100"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination Controls */}
          <div className="flex items-center justify-between px-4 py-4">
            <div className="text-sm text-muted-foreground">
              Showing {reviews.length} of {totalItems} reviews
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1 || isLoading}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1 || isLoading}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm px-2">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages || isLoading}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages || isLoading}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewsPage;
