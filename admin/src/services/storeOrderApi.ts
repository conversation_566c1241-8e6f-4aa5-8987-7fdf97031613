import { axiosInstance } from '@/lib/axios';

export interface StoreOrder {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string | null;
  itemId: string;
  itemName: string;
  itemPrice: number;
  quantity: number;
  totalCoins: number;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
  item: {
    id: string;
    name: string;
    description: string;
    coinPrice: number;
    quantity: number;
    category: string;
    image: string | null;
    status: string;
  };
}

export interface StoreOrderStats {
  totalOrders: number;
  completedOrders: number;
  pendingOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  todayOrders: number;
  thisMonthOrders: number;
}

export interface StoreOrderFilters {
  status?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
}

export const getAllStoreOrders = async (filters?: StoreOrderFilters) => {
  try {
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);
    if (filters?.startDate) params.append('startDate', filters.startDate);
    if (filters?.endDate) params.append('endDate', filters.endDate);

    const response = await axiosInstance.get(`/admin/store/orders/`);
    return response.data.data || [];
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store orders'
    };
  }
};

export const getStoreOrderStats = async () => {
  try {
    const response = await axiosInstance.get('/admin/store/orders/stats');
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store order statistics'
    };
  }
};

export const getStoreOrderById = async (orderId: string) => {
  try {
    const response = await axiosInstance.get(`/admin/store/orders/${orderId}`);
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store order'
    };
  }
};

export const updateStoreOrderStatus = async (orderId: string, status: string) => {
  try {
    const response = await axiosInstance.put(`/admin/store/orders/${orderId}`, { status });
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to update store order status'
    };
  }
};




