import { PrismaClient } from '@prisma/client';
import { PERMISSIONS } from '../config/constants';

const prisma = new PrismaClient();

export async function seedPermissions() {
  const seededPermissions: string[] = [];

  for (const module of Object.keys(PERMISSIONS)) {
    const permissions = PERMISSIONS[module];
    for (const permissionName of Object.keys(permissions)) {
      try {
        await prisma.permission.upsert({
          where: { permissionName },
          update: {},
          create: {
            id: crypto.randomUUID(),
            permissionName,
            createdAt: new Date(),
          },
        });
        seededPermissions.push(permissionName);
      } catch (error) {
        console.error(`Failed to seed permission ${permissionName}:`, error);
      }
    }
  }

  return seededPermissions;
}

if (require.main === module) {
  seedPermissions()
    .catch((e) => {
      console.error('Seeding permissions failed:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}