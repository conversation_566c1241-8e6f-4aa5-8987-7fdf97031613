import { Request, Response } from 'express';
import { processPurchase, getStudentOrders, getOrderById } from '../services/storePurchaseService';
import { sendSuccess, sendError } from '@/utils/response';

export const purchaseItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      sendError(res, 'Student authentication required', 401);
      return;
    }

    const { cartItems, totalCoins } = req.body;

    if (!cartItems || !Array.isArray(cartItems) || cartItems.length === 0) {
      sendError(res, 'Cart items are required', 400);
      return;
    }

    if (!totalCoins || totalCoins <= 0) {
      sendError(res, 'Invalid total coins amount', 400);
      return;
    }

    for (const item of cartItems) {
      if (!item.id || !item.name || !item.coinPrice || !item.quantity) {
        sendError(res, 'Invalid cart item structure', 400);
        return;
      }
    }

    const result = await processPurchase({
      studentId,
      cartItems,
      totalCoins
    });

    if (!result.success) {
      sendError(res, result.error, 400);
      return;
    }

    sendSuccess(res, result, 'Purchase completed successfully', 201);
  } catch (error: any) {
    sendError(res, error.message || 'Purchase failed', 500);
  }
};

export const getMyOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      sendError(res, 'Student authentication required', 401);
      return;
    }

    const result = await getStudentOrders(studentId);
    if (!result.success) {
      sendError(res, result.error, 500);
      return;
    }

    sendSuccess(res, result.data, 'Orders retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to fetch orders', 500);
  }
};

export const getOrderDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      sendError(res, 'Student authentication required', 401);
      return;
    }

    const { orderId } = req.params;
    if (!orderId) {
      sendError(res, 'Order ID is required', 400);
      return;
    }

    const result = await getOrderById(orderId, studentId);
    if (!result.success) {
      sendError(res, result.error, 404);
      return;
    }

    sendSuccess(res, result.data, 'Order details retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to fetch order details', 500);
  }
};
