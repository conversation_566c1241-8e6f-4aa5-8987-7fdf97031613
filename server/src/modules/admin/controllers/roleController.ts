import { Request, Response } from 'express';
import { getAllRoles as getAllRolesService, getRoleById as getRoleByIdService, createRole as createRoleService, updateRole as updateRoleService, deleteRole as deleteRoleService } from '../services/roleService';
import { sendSuccess, sendError } from '@/utils/response';

export const getAllRoles = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 10;
    const result = await getAllRolesService(page, pageSize);
    sendSuccess(res, result);
  } catch (error: any) {
    sendError(res, error.message || 'Failed to fetch roles', 500);
  }
};

export const getRoleById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const role = await getRoleByIdService(id);
    sendSuccess(res, role);
  } catch (error: any) {
    sendError(res, error.message || 'Failed to fetch role', 400);
  }
};

export const createRole = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, permissions } = req.body;
    if (!name || !permissions) {
      sendError(res, 'Role name and permissions are required', 400);
      return;
    }
    const role = await createRoleService(name, permissions);
    sendSuccess(res, role);
  } catch (error: any) {
    sendError(res, error.message || 'Failed to create role', 400);
  }
};

export const updateRole = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, permissions } = req.body;
    if (!name || !permissions) {
      sendError(res, 'Role name and permissions are required', 400);
      return;
    }
    const role = await updateRoleService(id, name, permissions);
    sendSuccess(res, role);
  } catch (error: any) {
    sendError(res, error.message || 'Failed to update role', 400);
  }
};

export const deleteRole = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const result = await deleteRoleService(id);
    sendSuccess(res, result);
  } catch (error: any) {
    sendError(res, error.message || 'Failed to delete role', 400);
  }
};