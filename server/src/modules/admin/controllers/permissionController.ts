import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import { permissionService } from '@/modules/admin/services/permissionService';

export const permissionController = {
  getPermissions: async (req: Request, res: Response) => {
    try {
      const permissions = await permissionService.getAllPermissions();
      sendSuccess(res, { permissions });
    } catch (error: any) {
      sendError(res, error.message || 'Failed to fetch permissions', 500);
    }
  },
};