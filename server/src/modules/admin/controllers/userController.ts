import { Request, Response } from 'express';
import { getUsers as getUsersService, getUserById as getUserByIdService, createUser as createUserService, updateUser as updateUserService, deleteUser as deleteUserService } from '../services/userService';
import { sendError, sendSuccess } from '@/utils/response';

export const getUsers = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 10;
    const { users, total } = await getUsersService(page, pageSize);
    sendSuccess(res, {
      users: users.map((user) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role ? { id: user.role.id, name: user.role.name } : null,
        createdAt: user.createdAt,
      })),
      total,
      totalPages: Math.ceil(total / pageSize),
      currentPage: page,
    });
  } catch (error: any) {
    sendError(res, error.message || 'Failed to fetch users', 500);
  }
};

export const getUserById = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = await getUserByIdService(req.params.id);
    if (!user) {
      sendError(res, 'User not found', 404);
      return;
    }
    sendSuccess(res, {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role ? { id: user.role.id, name: user.role.name } : null,
      roleId: user.role ? user.role.id : null,
      createdAt: user.createdAt,
    });
  } catch (error: any) {
    sendError(res, error.message || 'Failed to fetch user', 500);
  }
};

export const createUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, email, password, roleId } = req.body;
    if (!email || !password) {
      sendError(res, 'Email and password are required', 400);
      return;
    }
    const user = await createUserService({ name, email, password, roleId });
    sendSuccess(res, {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role ? { id: user.role.id, name: user.role.name } : null,
      createdAt: user.createdAt,
    });
  } catch (error: any) {
    sendError(res, error.message || 'Failed to create user', 400);
  }
};

export const updateUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, email, password, roleId } = req.body;
    const user = await updateUserService(id, { name, email, password, roleId });
    sendSuccess(res, {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role ? { id: user.role.id, name: user.role.name } : null,
      createdAt: user.createdAt,
    });
  } catch (error: any) {
    sendError(res, error.message || 'Failed to update user', 400);
  }
};

export const deleteUser = async (req: Request, res: Response): Promise<void> => {
  try {
    await deleteUserService(req.params.id);
    sendSuccess(res, { message: 'User deleted' });
  } catch (error: any) {
    sendError(res, error.message || 'Failed to delete user', 400);
  }
};