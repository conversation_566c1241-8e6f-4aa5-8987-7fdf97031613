import { Router } from 'express';
import {
  createCategoryController,
  getAllCategoriesController,
  getCategoryByIdController,
  updateCategoryController,
  deleteCategoryController,
  createDetailController,
  getDetailsByCategoryController,
  getDetailByIdController,
  updateDetailController,
  deleteDetailController,
  createSubDetailController,
  getSubDetailsByDetailController,
  getAllClassroomDataController,
  getSubDetailByIdController,
  updateSubDetailController,
  deleteSubDetailController,
  createValueController,
  getValuesBySubDetailController,
  getValueByIdController,
  updateValueController,
  deleteValueController,

} from '../controllers/constantController';
import {
  createCategorySchema,
  updateCategorySchema,
  createDetailSchema,
  updateDetailSchema,
  createSubDetailSchema,
  updateSubDetailSchema,
  createV<PERSON>ueSchema,
  updateValueSchema
} from '../requests/constantRequest';
import validateRequest from '@/middlewares/validateRequest';
import { authMiddleware, permissionMiddleware } from '@/middlewares/adminAuth';

const adminConstantRouter = Router();

adminConstantRouter.get('/classroom-data', getAllClassroomDataController);

// Apply auth middleware to all routes
adminConstantRouter.use(authMiddleware);

// ConstantCategory Routes
adminConstantRouter.post('/categories', permissionMiddleware('create constants'), validateRequest(createCategorySchema), createCategoryController);
adminConstantRouter.get('/categories', permissionMiddleware('read constants'), getAllCategoriesController); // Use admin controller with proper response format
adminConstantRouter.get('/categories/:id', permissionMiddleware('read constants'), getCategoryByIdController);
adminConstantRouter.put('/categories/:id', permissionMiddleware('update constants'), validateRequest(updateCategorySchema), updateCategoryController);
adminConstantRouter.delete('/categories/:id', permissionMiddleware('delete constants'), deleteCategoryController);

// ConstantDetail Routes
adminConstantRouter.post('/details', permissionMiddleware('create constants'), validateRequest(createDetailSchema), createDetailController);
adminConstantRouter.get('/details/category/:categoryId', permissionMiddleware('read constants'), getDetailsByCategoryController);
adminConstantRouter.get('/details/:id', permissionMiddleware('read constants'), getDetailByIdController);
adminConstantRouter.put('/details/:id', permissionMiddleware('update constants'), validateRequest(updateDetailSchema), updateDetailController);
adminConstantRouter.delete('/details/:id', permissionMiddleware('delete constants'), deleteDetailController);

// ConstantSubDetail Routes
adminConstantRouter.post('/sub-details', validateRequest(createSubDetailSchema), createSubDetailController);
adminConstantRouter.get('/sub-details/detail/:detailId', getSubDetailsByDetailController);
adminConstantRouter.get('/sub-details/:id', getSubDetailByIdController);
adminConstantRouter.put('/sub-details/:id', validateRequest(updateSubDetailSchema), updateSubDetailController);
adminConstantRouter.delete('/sub-details/:id', deleteSubDetailController);

// ConstantSubDetailValue Routes
adminConstantRouter.post('/values', validateRequest(createValueSchema), createValueController);
adminConstantRouter.get('/values/sub-detail/:subDetailId', getValuesBySubDetailController);
adminConstantRouter.get('/values/:id', getValueByIdController);
adminConstantRouter.put('/values/:id', validateRequest(updateValueSchema), updateValueController);
adminConstantRouter.delete('/values/:id', deleteValueController);

export default adminConstantRouter;
