import { Router } from 'express';
import * as storeController from '../controllers/storeController';
import * as storeOrderController from '../controllers/storeOrderController';
import { permissionMiddleware } from '@/middlewares/adminAuth';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    const dir = path.join('uploads', 'store');
    fs.mkdirSync(dir, { recursive: true });
    cb(null, dir);
  },
  filename: (_req, file, cb) => {
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext).replace(/\s+/g, '-');
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  },
});

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (_req, file, cb) => {
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('Only image files are allowed'));
    }
    cb(null, true);
  },
});

router.get('/', permissionMiddleware('read store'), storeController.getAllStoreItems);
router.get('/stats', permissionMiddleware('read store'), storeController.getStoreStats);
router.post('/', permissionMiddleware('create store'), upload.single('image'), storeController.createStoreItem);
router.put('/:id', permissionMiddleware('update store'), upload.single('image'), storeController.updateStoreItem);
router.delete('/:id', permissionMiddleware('delete store'), storeController.deleteStoreItem);

router.get('/orders', permissionMiddleware('read store'), storeOrderController.getAllOrders);
router.get('/orders/stats', permissionMiddleware('read store'), storeOrderController.getOrderStats);
router.get('/orders/:orderId', permissionMiddleware('read store'), storeOrderController.getOrderDetails);
router.put('/orders/:orderId', permissionMiddleware('update store'), storeOrderController.updateOrder);

router.get('/:id', permissionMiddleware('read store'), storeController.getStoreItemById);

export default router;
