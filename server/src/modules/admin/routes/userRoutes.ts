import { Router } from 'express';
import {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
} from '../controllers/userController';
import { permissionMiddleware } from '@/middlewares/adminAuth';

const userRouter = Router();

userRouter.get('/', permissionMiddleware('read users'), getUsers);
userRouter.get('/:id', permissionMiddleware('read users'), getUserById);
userRouter.post('/', permissionMiddleware('create users'), createUser);
userRouter.put('/:id', permissionMiddleware('update users'), updateUser);
userRouter.delete('/:id', permissionMiddleware('delete users'), deleteUser);

export default userRouter;