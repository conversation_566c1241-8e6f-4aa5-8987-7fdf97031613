import { Router } from 'express';
import {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
} from '../controllers/roleController';
import { permissionMiddleware } from '@/middlewares/adminAuth';

const roleRouter = Router();

roleRouter.get('/', permissionMiddleware('read role'), getAllRoles);
roleRouter.get('/:id', permissionMiddleware('read role'), getRoleById);
roleRouter.post('/', permissionMiddleware('create role'), createRole);
roleRouter.put('/:id', permissionMiddleware('update role'), updateRole);
roleRouter.delete('/:id', permissionMiddleware('delete role'), deleteRole);

export default roleRouter;