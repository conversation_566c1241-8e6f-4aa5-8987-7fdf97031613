import prisma from '@/config/prismaClient';
import bcrypt from 'bcryptjs';

export const getUsers = async (page: number, pageSize: number) => {
  const skip = (page - 1) * pageSize;
  const [users, total] = await Promise.all([
    prisma.adminUser.findMany({
      skip,
      take: pageSize,
      include: { role: true },
    }),
    prisma.adminUser.count(),
  ]);
  return { users, total };
};

export const getUserById = async (id: string) => {
  return prisma.adminUser.findUnique({
    where: { id },
    include: { role: true },
  });
};

export const createUser = async (data: { name: string; email: string; password: string; roleId: string }) => {
  const hashedPassword = await bcrypt.hash(data.password, 10);
  return prisma.adminUser.create({
    data: {
      id: crypto.randomUUID(),
      name: data.name,
      email: data.email,
      password: hashedPassword,
      roleId: data.roleId,
      createdAt: new Date(),
    },
    include: { role: true },
  });
};

export const updateUser = async (id: string, data: { name: string; email: string; password?: string; roleId?: string }) => {
  const updateData: any = { name: data.name, email: data.email };
  if (data.password) {
    updateData.password = await bcrypt.hash(data.password, 10);
  }
  if (data.roleId) {
    updateData.roleId = data.roleId;
  }
  return prisma.adminUser.update({
    where: { id },
    data: updateData,
    include: { role: true },
  });
};

export const deleteUser = async (id: string) => {
  return prisma.adminUser.delete({ where: { id } });
};

export const getRoleName = async (roleId: string) => {
  const role = await prisma.role.findUnique({ where: { id: roleId } });
  return role ? role.name : 'N/A';
};