import prisma from '@/config/prismaClient';

export const getAllRoles = async (page: number, pageSize: number) => {
  try {
    const skip = (page - 1) * pageSize;
    const [roles, total] = await Promise.all([
      prisma.role.findMany({
        skip,
        take: pageSize,
        include: { permissions: { include: { permission: true } } },
      }),
      prisma.role.count(),
    ]);

    const totalPages = Math.ceil(total / pageSize);
    return {
      roles: roles.map((role) => ({
        ...role,
        permissions: role.permissions.map((p) => p.permission),
      })),
      total,
      totalPages,
    };
  } catch {
    throw new Error('Failed to fetch roles');
  }
};

export const getRoleById = async (id: string) => {
  try {
    const role = await prisma.role.findUnique({
      where: { id },
      include: { permissions: { include: { permission: true } } },
    });
    if (!role) {
      throw new Error('Role not found');
    }
    return {
      ...role,
      permissions: role.permissions.map((p) => p.permission),
    };
  } catch (error: any) {
    throw error.message === 'Role not found' ? error : new Error('Failed to fetch role');
  }
};

export const createRole = async (name: string, permissions: string[]) => {
  try {
    const existingPermissions = await prisma.permission.findMany({
      where: { permissionName: { in: permissions } },
      select: { permissionName: true },
    });
    const validPermissions = existingPermissions.map((p) => p.permissionName);
    const invalidPermissions = permissions.filter((p) => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      throw new Error(`Invalid permissions: ${invalidPermissions.join(', ')}`);
    }

    const role = await prisma.role.create({
      data: {
        name,
        permissions: {
          create: permissions.map((perm) => ({
            permission: { connect: { permissionName: perm } },
          })),
        },
      },
      include: { permissions: { include: { permission: true } } },
    });
    return {
      ...role,
      permissions: role.permissions.map((p) => p.permission),
    };
  } catch (error: any) {
    if (error.code === 'P2002') {
      throw new Error(`Role name '${name}' already exists`);
    }
    throw new Error('Failed to create role');
  }
};

export const updateRole = async (id: string, name: string, permissions: string[]) => {
  try {
    const existingPermissions = await prisma.permission.findMany({
      where: { permissionName: { in: permissions } },
      select: { permissionName: true },
    });
    const validPermissions = existingPermissions.map((p) => p.permissionName);
    const invalidPermissions = permissions.filter((p) => !validPermissions.includes(p));
    if (invalidPermissions.length > 0) {
      throw new Error(`Invalid permissions: ${invalidPermissions.join(', ')}`);
    }

    await prisma.roleHasPermission.deleteMany({
      where: { roleId: id },
    });

    const role = await prisma.role.update({
      where: { id },
      data: {
        name,
        permissions: {
          create: permissions.map((perm) => ({
            permission: { connect: { permissionName: perm } },
          })),
        },
      },
      include: { permissions: { include: { permission: true } } },
    });
    return {
      ...role,
      permissions: role.permissions.map((p) => p.permission),
    };
  } catch (error: any) {
    if (error.code === 'P2002') {
      throw new Error(`Role name '${name}' already exists`);
    }
    if (error.code === 'P2025') {
      throw new Error('Role not found');
    }
    throw new Error('Failed to update role');
  }
};

export const deleteRole = async (id: string) => {
  try {
    await prisma.role.delete({ where: { id } });
    return { message: 'Role deleted' };
  } catch (error: any) {
    if (error.code === 'P2025') {
      throw new Error('Role not found');
    }
    throw new Error('Failed to delete role');
  }
};